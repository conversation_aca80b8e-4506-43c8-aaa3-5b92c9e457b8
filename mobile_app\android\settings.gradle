pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            url 'https://storage.googleapis.com/download.flutter.io'
        }
    }
}

// Plugin patches
def patchedPlugins = [
    'flutter_secure_storage': 'plugin_patches/flutter_secure_storage',
    'root_check': 'plugin_patches/root_check',
    'flutter_jailbreak_detection': 'plugin_patches/flutter_jailbreak_detection',
    'battery_plus': 'plugin_patches/battery_plus',
    'sensors_plus': 'plugin_patches/sensors_plus',
    'shared_preferences_android': 'plugin_patches/shared_preferences_android'
]

// Configure plugin patches
gradle.ext.patchedPlugins = patchedPlugins

def flutterProjectRoot = rootProject.projectDir.parentFile.toPath()
def plugins = new Properties()
def pluginsFile = new File(flutterProjectRoot.toFile(), '.flutter-plugins')
if (pluginsFile.exists()) {
    pluginsFile.withReader('UTF-8') { reader -> plugins.load(reader) }
}

include ":flutter"
project(":flutter").projectDir = new File("${flutterProjectRoot}/.android/Flutter")

include ":app"

// Include patched plugins
patchedPlugins.each { name, path ->
    include ":$name"
    project(":$name").projectDir = new File("$rootDir/$path")
}
