allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://storage.googleapis.com/download.flutter.io'
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion 34

                if (project.hasProperty("buildTypes")) {
                    buildTypes {
                        release {
                            minifyEnabled false
                            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                        }
                    }
                }

                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
            }
        }

        // Configure JVM toolchain for all projects
        if (project.hasProperty("kotlin")) {
            project.kotlin {
                jvmToolchain(17)
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
